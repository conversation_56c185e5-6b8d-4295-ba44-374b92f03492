#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试详细的日志输出
验证SSE连接检查的详细日志
"""

import asyncio
import sys
import os
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.domain.services.message_processor import MessageProcessor
from memory.events import EventType


class TestEvent:
    """测试事件类"""
    def __init__(self, event_type, session_id="test_session", run_id="test_run"):
        self.event_id = f"test_event_{event_type.value}"
        self.type = event_type
        self.session_id = session_id
        self.run_id = run_id
        self.content = f"测试内容 for {event_type.value}"
        self.role = "assistant"
        self.message_id = "test_message"
        self.timestamp = 1755080000000

    def __str__(self):
        return f"TestEvent(id={self.event_id}, type={self.type})"


class MockSSEManager:
    """模拟SSE管理器"""
    def __init__(self, has_connection=False):
        self.sse_connections = {}
        if has_connection:
            self.sse_connections["test_session"] = Mock()


async def test_detailed_logging():
    """测试详细日志输出"""
    print("🔍 测试详细的SSE连接检查日志")
    print("=" * 50)
    
    processor = MessageProcessor()
    
    # 测试场景
    scenarios = [
        {
            "name": "无SSE管理器",
            "sse_manager": None,
            "event_type": EventType.TEXT_MESSAGE_CONTENT
        },
        {
            "name": "有SSE管理器但无连接",
            "sse_manager": MockSSEManager(has_connection=False),
            "event_type": EventType.TEXT_MESSAGE_CONTENT
        },
        {
            "name": "有SSE管理器且有连接",
            "sse_manager": MockSSEManager(has_connection=True),
            "event_type": EventType.TEXT_MESSAGE_CONTENT
        },
        {
            "name": "无SSE连接但是完成事件",
            "sse_manager": MockSSEManager(has_connection=False),
            "event_type": EventType.RUN_FINISHED
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n📋 场景 {i}: {scenario['name']}")
        print("-" * 30)
        
        # 设置SSE管理器
        processor.set_sse_manager(scenario['sse_manager'])
        
        # 创建测试事件
        event = TestEvent(scenario['event_type'])
        
        with patch.object(processor, '_load_session_from_db', return_value=Mock()) as mock_load:
            try:
                # 执行消息处理
                await processor._handle_new_message_internal("test_session", "test_run", event)
                print("✅ 处理完成")
            except Exception as e:
                print(f"⚠️ 处理异常: {e}")


if __name__ == "__main__":
    asyncio.run(test_detailed_logging())
