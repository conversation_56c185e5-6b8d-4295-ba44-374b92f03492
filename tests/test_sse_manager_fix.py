#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试SSE管理器修复
验证MemorySDK中的MessageProcessor是否正确设置了SSE管理器
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


async def test_sse_manager_setup():
    """测试SSE管理器设置"""
    print("🔧 测试SSE管理器设置修复")
    print("=" * 50)
    
    try:
        # 导入服务
        from src.domain.services.session_service import session_service
        from src.infrastructure.memory.memory_sdk import memory_sdk
        
        print("📋 检查服务状态:")
        
        # 检查SessionService
        if hasattr(session_service, 'message_processor'):
            print("✅ SessionService.message_processor 存在")
            
            if hasattr(session_service.message_processor, 'sse_manager'):
                if session_service.message_processor.sse_manager is not None:
                    print("✅ SessionService.message_processor.sse_manager 已设置")
                    print(f"   类型: {type(session_service.message_processor.sse_manager)}")
                else:
                    print("❌ SessionService.message_processor.sse_manager 为 None")
            else:
                print("❌ SessionService.message_processor 没有 sse_manager 属性")
        else:
            print("❌ SessionService 没有 message_processor 属性")
        
        # 检查MemorySDK
        if hasattr(memory_sdk, 'message_processor'):
            if memory_sdk.message_processor is not None:
                print("✅ MemorySDK.message_processor 存在")
                print(f"   类型: {type(memory_sdk.message_processor)}")
                
                if hasattr(memory_sdk.message_processor, 'sse_manager'):
                    if memory_sdk.message_processor.sse_manager is not None:
                        print("✅ MemorySDK.message_processor.sse_manager 已设置")
                        print(f"   类型: {type(memory_sdk.message_processor.sse_manager)}")
                        
                        # 检查是否是同一个实例
                        if (hasattr(session_service, 'message_processor') and 
                            memory_sdk.message_processor is session_service.message_processor):
                            print("✅ MemorySDK 和 SessionService 使用同一个 MessageProcessor 实例")
                        else:
                            print("⚠️ MemorySDK 和 SessionService 使用不同的 MessageProcessor 实例")
                            
                    else:
                        print("❌ MemorySDK.message_processor.sse_manager 为 None")
                else:
                    print("❌ MemorySDK.message_processor 没有 sse_manager 属性")
            else:
                print("❌ MemorySDK.message_processor 为 None")
        else:
            print("❌ MemorySDK 没有 message_processor 属性")
        
        # 测试消息处理
        print("\n📝 测试消息处理:")
        
        if (memory_sdk.message_processor and 
            hasattr(memory_sdk.message_processor, 'sse_manager') and
            memory_sdk.message_processor.sse_manager is not None):
            
            # 创建测试事件
            from memory.events import EventType
            
            class TestEvent:
                def __init__(self):
                    self.event_id = "test_event_fix"
                    self.type = EventType.TEXT_MESSAGE_CONTENT
                    self.session_id = "test_session_fix"
                    self.run_id = "test_run_fix"
                    self.content = "测试SSE管理器修复"
                    self.role = "assistant"
                    self.message_id = "test_message_fix"
                    self.timestamp = 1755080000000

                def __str__(self):
                    return f"TestEvent(id={self.event_id}, type={self.type})"
            
            event = TestEvent()
            print(f"   创建测试事件: {event}")
            
            # 直接调用消息处理
            await memory_sdk.message_processor.handle_new_message(event)
            
            print("✅ 消息处理测试完成")
        else:
            print("❌ 无法进行消息处理测试，SSE管理器未正确设置")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    success = await test_sse_manager_setup()
    
    if success:
        print("\n🎉 SSE管理器设置修复验证成功！")
        print("\n📋 修复总结:")
        print("✅ MemorySDK 现在使用 SessionService 中配置好的 MessageProcessor")
        print("✅ MessageProcessor 正确设置了 SSE 管理器")
        print("✅ 消息处理不再因为 'SSE管理器不存在' 而跳过")
    else:
        print("\n💥 SSE管理器设置修复验证失败")


if __name__ == "__main__":
    asyncio.run(main())
