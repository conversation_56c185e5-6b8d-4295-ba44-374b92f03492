"""
消息处理器
处理Memory SDK回调和消息格式化
"""

import asyncio
from datetime import datetime
from typing import Dict, Any, Optional, TYPE_CHECKING
from loguru import logger

from waiy_memory import Role
from ..models import Session, SessionStatus

from memory.events import Event,EventType


class MessageProcessor:
    """消息处理器 - 处理Memory SDK回调和消息格式化"""
    
    def __init__(self):
        self.sse_manager = None  # 稍后由SessionManager注入
        self.session_manager = None  # 稍后由SessionManager注入
        
    def set_sse_manager(self, sse_manager):
        """设置SSE管理器引用"""
        self.sse_manager = sse_manager
    
    def set_session_manager(self, session_manager):
        """设置Session管理器引用"""
        self.session_manager = session_manager

    async def handle_new_message(self, event: 'Event'):
        """处理新消息 - MemorySDK回调接口"""
        logger.info(f"handle_new_message: {event}")
        session_id = event.session_id or "default_session"
        round_id = event.run_id

        await self._handle_new_message_internal(session_id, round_id, event)
        
    async def _handle_new_message_internal(self, session_id: str, round_id: str, event: 'Event'):
        """处理新消息"""
        try:
            # 安全获取事件类型
            event_type_str = event.type.value if hasattr(event.type, 'value') else str(event.type)
            logger.info(f"[MessageProcessor] 新消息: session_id={session_id}, round_id={round_id}, event_type={event_type_str}")

            # 安全检查ARTIFACT事件类型
            if getattr(event, 'type', None):
                artifact_type_value = EventType.ARTIFACT.value if hasattr(EventType.ARTIFACT, 'value') else str(EventType.ARTIFACT)
                current_type_value = event.type.value if hasattr(event.type, 'value') else str(event.type)
                if current_type_value == artifact_type_value:
                    # 从数据库加载Session
                    session = self._load_session_from_db(session_id)
                    if not session:
                        logger.warning(f"[MessageProcessor] Session不存在: {session_id}")
                        return
                    await self._handle_artifact_event(session_id, round_id, event, session)

            # 判断是否为会话完成事件
            is_session_finish_event = False
            if getattr(event, 'type', None):
                run_finished_value = EventType.RUN_FINISHED.value if hasattr(EventType.RUN_FINISHED, 'value') else str(EventType.RUN_FINISHED)
                run_error_value = EventType.RUN_ERROR.value if hasattr(EventType.RUN_ERROR, 'value') else str(EventType.RUN_ERROR)
                current_type_value = event.type.value if hasattr(event.type, 'value') else str(event.type)
                is_session_finish_event = current_type_value in [run_finished_value, run_error_value]

            # 检查当前机器是否有该session的SSE连接
            # 对于会话完成事件，即使没有SSE连接也要处理，确保会话状态正确更新
            has_sse_connection = self.sse_manager and session_id in self.sse_manager.sse_connections

            if not has_sse_connection and not is_session_finish_event:
                logger.info(f"[MessageProcessor] 跳过消息处理，当前机器无此session连接且非会话完成事件: session_id={session_id}, event_type={event_type_str}")
                return

            # 从数据库加载Session
            session = self._load_session_from_db(session_id)
            if not session:
                logger.warning(f"[MessageProcessor] Session不存在: {session_id}")
                return

            # 推送到SSE - 使用Event数据（只在有SSE连接时推送）
            if has_sse_connection:
                # 手动构建字典确保枚举值正确序列化
                sse_data = self._serialize_event_for_sse(event)
                await self.sse_manager.push_to_sse(session_id, round_id, sse_data)

            # 特殊处理：如果是finish消息，则完成Session
            if is_session_finish_event:
                logger.info(f"[MessageProcessor] 收到finish消息，完成Session: session_id={session_id}")

                # 发送完成事件到SSE（只在有SSE连接时发送）
                if has_sse_connection:
                    done_data = self._format_done_event_data(round_id, session_id, event)
                    await self.sse_manager.push_to_sse(session_id, round_id, done_data, event_type="done")

                # 完成Session处理，流转到CLOSED状态
                session.finish_processing()
                logger.info(f"[MessageProcessor] Session状态已流转到CLOSED: session_id={session_id}")

                # 等待一小段时间确保done事件发送完成，然后关闭SSE连接（只在有SSE连接时关闭）
                if has_sse_connection:
                    await asyncio.sleep(0.5)
                    self.sse_manager.close_session_connection(session_id)
                    logger.info(f"[MessageProcessor] SSE连接已关闭: session_id={session_id}")

        except Exception as e:
            import traceback
            logger.error(f"[MessageProcessor] 处理新消息失败: {e} event: {getattr(event, 'event_id', 'None') if event else 'None'}")
            logger.error(f"[MessageProcessor] 错误堆栈: {traceback.format_exc()}")
    

    def _load_session_from_db(self, session_id: str) -> Optional[Session]:
        """从数据库加载Session"""
        try:
            from ...infrastructure.database.repositories.session_repository import session_db_service
            session_model = session_db_service.get_session_by_id(session_id)
            if not session_model:
                return None
            
            # 从数据库模型重建Session对象
            session = Session(
                session_id=session_model.session_id,
                ali_uid=session_model.ali_uid,
                wy_id=session_model.wy_id,
                agent_id=session_model.agent_id,
                title=session_model.title,
                status=session_model.status,
                gmt_create=session_model.gmt_create,
                gmt_modified=session_model.gmt_modified,
                metadata=session_model.meta_data or {}
            )
            
            # 设置事件监听，确保状态变更能同步到数据库
            if self.session_manager:
                self.session_manager.setup_session_events(session)
            
            return session
            
        except Exception as e:
            logger.error(f"[MessageProcessor] 从数据库加载Session失败: {e}")
            return None

    def _serialize_event_for_sse(self, event: 'Event') -> Dict[str, Any]:
        """序列化Event对象为SSE数据，确保枚举值正确转换"""
        try:
            # 获取基础数据
            data = event.model_dump()

            # 手动处理枚举字段
            if hasattr(event, 'type') and event.type:
                data['type'] = event.type.value if hasattr(event.type, 'value') else str(event.type)

            # 处理其他可能的枚举字段
            if hasattr(event, 'role') and event.role:
                data['role'] = event.role.value if hasattr(event.role, 'value') else str(event.role)

            return data
        except Exception as e:
            logger.warning(f"[MessageProcessor] Event序列化失败，使用fallback: {e}")
            # 如果序列化失败，使用基本的字典构建
            return {
                'event_id': getattr(event, 'event_id', ''),
                'type': event.type.value if hasattr(event, 'type') and hasattr(event.type, 'value') else str(getattr(event, 'type', '')),
                'timestamp': getattr(event, 'timestamp', None),
                'session_id': getattr(event, 'session_id', ''),
                'run_id': getattr(event, 'run_id', ''),
                'content': getattr(event, 'content', ''),
                'role': getattr(event, 'role', None)
            }
    
    def _process_message_content(self, content: Any) -> str:
        """处理消息内容"""
        if isinstance(content, dict):
            return content.get("text", str(content))
        else:
            return str(content)

    def _convert_event_to_message(self, event: 'Event') -> 'Message':
        """将Event转换为Message对象"""
        from waiy_memory import Message, Role

        # 确定消息角色
        tool_name = getattr(event, 'tool_name', None)
        role = getattr(event, 'role', None)

        if tool_name:
            message_role = Role.TOOL
        elif role == "assistant":
            message_role = Role.ASSISTANT
        elif role == "user":
            message_role = Role.USER
        elif role == "system":
            message_role = Role.SYSTEM
        else:
            message_role = Role.ASSISTANT  # 默认助手消息

        # 获取内容，优先使用 content，然后是 delta 或 message
        content = getattr(event, 'content', None)
        if not content:
            content = getattr(event, 'delta', None)
        if not content:
            content = getattr(event, 'message', None)

        return Message(
            message_id=getattr(event, 'message_id', None),
            role=message_role,
            content=content or "",
            session_id=event.session_id,
            run_id=event.run_id,
            tool_name=tool_name,
            tool_call_id=getattr(event, 'tool_call_id', None)
        )

    def _convert_event_to_sse_data(self, event: 'Event') -> Dict[str, Any]:
        """将Event转换为SSE推送需要的数据格式"""
        # 获取内容，优先使用 content，然后是 delta 或 message
        content = getattr(event, 'content', None)
        if not content:
            content = getattr(event, 'delta', None)
        if not content:
            content = getattr(event, 'message', None)

        return {
            "messageId": getattr(event, 'message_id', None),
            "role": getattr(event, 'role', None) or "assistant",
            "roundId": event.run_id,
            "content": content or "",
            "timestamp": getattr(event, 'gmt_create', None).isoformat() if getattr(event, 'gmt_create', None) else datetime.now().isoformat(),
            "sessionId": event.session_id,
            "toolName": getattr(event, 'tool_name', None),
            "toolCallId": getattr(event, 'tool_call_id', None),
            "eventId": event.event_id,
            "eventType": event.type.value if hasattr(event.type, 'value') else str(event.type)
        }
    
    def _format_done_event_data(self, round_id: str, session_id: str, event: 'Event') -> Dict[str, Any]:
        """格式化完成事件数据"""
        # 获取内容，优先使用 content，然后是 delta 或 message
        content = getattr(event, 'content', None)
        if not content:
            content = getattr(event, 'delta', None)
        if not content:
            content = getattr(event, 'message', None)

        return {
            "type": "done",
            "data": {
                "roundId": round_id,
                "sessionId": session_id,
                "status": "completed",
                "endTime": datetime.now().isoformat(),
                "content": content or "",
                "metadata": {}  # Event暂时不包含metadata
            }
        }

    async def _handle_artifact_event(self, session_id: str, round_id: str, event: 'Event', session: Session):
        """处理ARTIFACT类型的Event"""
        try:
            logger.info(f"[MessageProcessor] 处理ARTIFACT事件: session_id={session_id}, round_id={round_id}")

            # 导入ArtifactEvent类型
            from memory.events import ArtifactEvent
            
            # 1. 首先检查event是否为ArtifactEvent类型
            if not isinstance(event, ArtifactEvent):
                logger.warning(f"[MessageProcessor] Event不是ArtifactEvent类型: {type(event)}")
                return
            
            # 2. 现在可以安全地使用ArtifactEvent的属性
            artifact_event: ArtifactEvent = event
            
            # 3. 从session中提取wy_id、uid，从event中提取file_name、is_process_file（是否是结果制品）
            wy_id = session.wy_id
            ali_uid = session.ali_uid
            artifact_id = artifact_event.artifact_id
            file_name = artifact_event.file_name
            is_process_file = artifact_event.is_process_file
            file_content = artifact_event.content
            
            logger.info(f"[MessageProcessor] 制品信息: file_name={file_name}, is_process_file={is_process_file},  artifact_id={artifact_id}")


            # 5. 将file_content内容保存成{file_name}.{file_type}文件，并上传到AlphaService的OSS中，
            # 制品的存储路径为：f"artifacts/result/{ali_uid}/{artifact_id}/{file_name}.{file_type}" 或 f"artifacts/process/{ali_uid}/{filename}"
            from ...infrastructure.oss.oss_service import oss_service
            
            if file_content and file_name:
                # 清理文件内容，去掉markdown代码标记
                cleaned_content = self._clean_file_content(file_content)

                # 确定文件类型和完整文件名
                import mimetypes

                # 如果文件名已经有扩展名，使用原文件名；否则尝试根据内容类型添加扩展名
                if '.' in file_name:
                    full_file_name = file_name
                else:
                    # 尝试从artifact_event中获取文件类型
                    file_type = getattr(artifact_event, 'file_type', None)
                    if not file_type:
                        # 根据内容猜测文件类型（使用清理后的内容）
                        if cleaned_content.strip().startswith('<'):
                            file_type = 'html'
                        elif cleaned_content.strip().startswith('{') or cleaned_content.strip().startswith('['):
                            file_type = 'json'
                        else:
                            file_type = 'txt'
                    full_file_name = f"{file_name}.{file_type}"
                
                # 确定OSS存储路径
                if is_process_file:
                    # 结果制品
                    target_object_name = f"artifacts/result/{ali_uid}/{artifact_id}/{full_file_name}"
                else:
                    # 过程制品
                    target_object_name = f"artifacts/process/{ali_uid}/{artifact_id}/{full_file_name}"
                
                logger.info(f"[MessageProcessor] 开始上传制品文件: {full_file_name} -> {target_object_name}")
                
                try:
                    # 将清理后的文件内容转换为字节流
                    if isinstance(cleaned_content, str):
                        file_bytes = cleaned_content.encode('utf-8')
                    else:
                        file_bytes = cleaned_content
                    
                    # 确定MIME类型
                    content_type, _ = mimetypes.guess_type(full_file_name)
                    if not content_type:
                        content_type = 'application/octet-stream'
                    
                    # 上传文件到OSS
                    upload_result = oss_service.upload_bytes(
                        data=file_bytes,
                        key=target_object_name,
                        content_type=content_type
                    )
                    
                    if upload_result.success:
                        logger.info(f"[MessageProcessor] 制品文件上传成功: {target_object_name}")
                        
                        # 6. 调用文件服务进行文件注册、鉴权注册
                        from ...domain.services.file_service import file_service
                        
                        file_obj = file_service.register_artifact(
                            ali_uid=str(ali_uid),
                            wy_id=wy_id,
                            session_id=session_id,
                            artifact_id=artifact_id,
                            file_name=full_file_name,
                            file_path=target_object_name,
                            bucket_name=oss_service.config.bucket_name, 
                            is_process_file=is_process_file,
                            content=cleaned_content,
                            file_size=len(file_bytes),
                            content_type=content_type,
                        )
                        
                        if file_obj:
                            logger.info(f"[MessageProcessor] 制品文件注册成功: file_id={file_obj.id}, file_name={file_obj.title}")
                        else:
                            logger.error(f"[MessageProcessor] 制品文件注册失败")
                    else:
                        logger.error(f"[MessageProcessor] 制品文件上传失败: {upload_result.error_message}")
                        
                except Exception as upload_error:
                    logger.error(f"[MessageProcessor] 制品文件上传异常: {upload_error}")
            else:
                logger.warning(f"[MessageProcessor] 制品信息不完整: file_content={bool(file_content)}, file_name={file_name}")

        except Exception as e:
            logger.error(f"[MessageProcessor] 制品处理失败: {e}")
            # 不抛出异常，确保不影响正常的消息处理流程

    def _clean_file_content(self, content: str) -> str:
        """
        清理文件内容，去掉markdown代码标记

        Args:
            content: 原始文件内容

        Returns:
            str: 清理后的文件内容
        """
        if not isinstance(content, str):
            return content

        # 去掉HTML的markdown代码标记
        import re

        # 匹配 ```html 开头和 ``` 结尾的代码块
        html_pattern = r'^```html\s*\n(.*?)\n```$'
        match = re.match(html_pattern, content.strip(), re.DOTALL | re.IGNORECASE)
        if match:
            logger.info("[MessageProcessor] 检测到HTML markdown代码块，正在清理...")
            return match.group(1).strip()

        # 如果没有匹配到代码块标记，返回原内容
        return content

    async def handle_round_failed(self, event: 'Event'):
        """处理轮次失败 - MemorySDK回调接口"""
        try:
            session_id = event.session_id or "default_session"
            round_id = event.run_id
            logger.error(f"[MessageProcessor] 轮次失败: session_id={session_id}, round_id={round_id}")

            # 这里可以添加轮次失败的处理逻辑
            # 比如通知前端、记录日志等

        except Exception as e:
            logger.error(f"[MessageProcessor] 处理轮次失败异常: {e}")
